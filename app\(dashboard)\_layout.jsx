import { useLocal<PERSON><PERSON>ch<PERSON><PERSON><PERSON>, useRouter } from "expo-router"
import { useColorScheme, View, TouchableOpacity, Image, StyleSheet, Dimensions } from "react-native"
import { useEffect, useState, useRef, useCallback, memo } from "react"
import { Colors } from "../../constants/Colors"
import { useUser } from "../../hooks/useUser"
import AsyncStorage from '@react-native-async-storage/async-storage'
import { shadowPresets } from "../../utils/shadowUtils"
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing
} from 'react-native-reanimated'

import UserOnly from "../../components/auth/UserOnly"
import GuestPermissionModal from "../../components/GuestPermissionModal"
import { useGuestPermission } from "../../hooks/useGuestPermission"
import ThemedText from "../../components/ThemedText"

// 导入页面组件
import IndexPage from "./index"
import PlansPage from "./plans"
import CommunityPage from "./community"
import ProfilePage from "./profile"

const { width: screenWidth } = Dimensions.get('window')

// 定义颜色主题
const tabColors = {
  index: { bg: '#e3f2fd', slider: '#CDEAFA' },
  plans: { bg: '#e8f5e9', slider: '#E2DCF1' },
  more: { bg: '#fff3e0', slider: '#FF8D00' },
  community: { bg: '#f3e5f5', slider: '#DBF1CD' },
  profile: { bg: '#ffebee', slider: '#FFE2CD' }
};

export default function DashboardLayout() {
  const colorScheme = useColorScheme()
  const theme = Colors[colorScheme] ?? Colors.light
  const { tab } = useLocalSearchParams()
  const { user, authChecked } = useUser()
  const router = useRouter()
  const [ready, setReady] = useState(false)
  const [activeTab, setActiveTab] = useState('index')
  const [showMorePanel, setShowMorePanel] = useState(false)

  // 游客权限管理
  const {
    tryAccessPage,
    showPermissionModal,
    currentFeature,
    handleRegister,
    closePermissionModal
  } = useGuestPermission()

  // 动画值
  const tabBarContentWidth = screenWidth - 40;
  const actualTabWidth = tabBarContentWidth / 5;
  // 简化初始化：先设为0，后续通过useEffect设置正确位置
  const sliderTranslateX = useSharedValue(0)
  const sliderScale = useSharedValue(1)
  const sliderColor = useSharedValue(tabColors.index.slider)
  // 添加共享值来跟踪当前选中的tab索引，用于同步所有动画
  const currentTabIndex = useSharedValue(0) // 0 = index, 1 = plans, 2 = more, 3 = community, 4 = profile
  const iconScales = useRef([
    useSharedValue(1),
    useSharedValue(1),
    useSharedValue(1),
    useSharedValue(1),
    useSharedValue(1)
  ]).current

  // 为每个图标创建动画样式
  const iconAnimatedStyles = [
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[0].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[1].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[2].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[3].value }],
    })),
    useAnimatedStyle(() => ({
      transform: [{ scale: iconScales[4].value }],
    })),
  ];

  // 基于共享值的图标未选中状态动画 - 确保与滑块动画同步
  const iconInactiveStyles = [
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 0 ? 0 : 1, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 0 ? '-180deg' : '0deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 1 ? 0 : 1, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 1 ? '-180deg' : '0deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 2 ? 0 : 1, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 2 ? '-180deg' : '0deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 3 ? 0 : 1, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 3 ? '-180deg' : '0deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 4 ? 0 : 1, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 4 ? '-180deg' : '0deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
  ];

  // 基于共享值的图标选中状态动画 - 确保与滑块动画同步
  const iconActiveStyles = [
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 0 ? 1 : 0, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 0 ? '0deg' : '180deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 1 ? 1 : 0, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 1 ? '0deg' : '180deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 2 ? 1 : 0, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 2 ? '0deg' : '180deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 3 ? 1 : 0, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 3 ? '0deg' : '180deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
    useAnimatedStyle(() => ({
      opacity: withTiming(currentTabIndex.value === 4 ? 1 : 0, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      }),
      transform: [
        {
          rotate: withTiming(currentTabIndex.value === 4 ? '0deg' : '180deg', {
            duration: 200,
            easing: Easing.out(Easing.quad)
          })
        }
      ]
    })),
  ];

  // more 面板的动画样式
  const morePanelAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(showMorePanel ? 1 : 0, { duration: 250 }),
      transform: [
        {
          translateY: withTiming(showMorePanel ? 0 : 10, {
            duration: 250
          })
        },
        {
          scale: withTiming(showMorePanel ? 1 : 0.98, {
            duration: 300
          })
        }
      ]
    };
  });



  // 智能设置滑块位置的函数（支持动画和立即设置）
  const setSliderPosition = useCallback((tabName, immediate = false) => {
    const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
    const tabIndex = tabIndexMap[tabName] || 0;
    const tabBarContentWidth = screenWidth - 40;
    const actualTabWidth = tabBarContentWidth / 5;
    const targetPosition = actualTabWidth * tabIndex;

    console.log('设置滑块位置:', {
      tabName,
      tabIndex,
      targetPosition,
      immediate,
      currentPosition: sliderTranslateX.value
    });

    if (immediate) {
      // 立即设置，不使用动画（用于初始化）
      console.log('立即设置滑块位置到:', targetPosition);
      sliderTranslateX.value = targetPosition;
      sliderColor.value = tabColors[tabName]?.slider || tabColors.index.slider;
    } else {
      // 使用平滑的动画配置
      console.log('使用动画设置滑块位置到:', targetPosition);
      sliderTranslateX.value = withTiming(targetPosition, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1) // 更平滑的贝塞尔曲线
      });
      sliderColor.value = withTiming(tabColors[tabName]?.slider || tabColors.index.slider, {
        duration: 300,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1)
      });
    }
  }, [sliderTranslateX, sliderColor, screenWidth]);

  // 监听activeTab变化，同步更新滑块位置（仅在初始化时使用）
  // 注释掉这个useEffect，避免与handleTabPress中的直接动画冲突
  // useEffect(() => {
  //   if (ready && activeTab && !user) {
  //     // 只在用户状态未确定时使用，避免与用户交互冲突
  //     console.log('初始化时activeTab变化，更新滑块位置:', activeTab);
  //     setSliderPosition(activeTab, true); // 初始化时使用立即设置，避免不必要的动画
  //   }
  // }, [activeTab, ready, user, setSliderPosition]);

  // 用户状态确定时的初始化（只在首次加载时执行）
  useEffect(() => {
    if (user && !ready) {
      // 默认都从index开始，不再根据用户类型设置不同的初始tab
      const initialTab = 'index';
      console.log('首次用户状态确定，初始化滑块到:', initialTab);

      // 计算滑块位置
      const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
      const tabIndex = tabIndexMap[initialTab] || 0;
      const tabBarContentWidth = screenWidth - 40;
      const actualTabWidth = tabBarContentWidth / 5;
      const targetPosition = actualTabWidth * tabIndex;

      // 立即设置activeTab和滑块位置
      setActiveTab(initialTab);
      sliderTranslateX.value = targetPosition;
      sliderColor.value = tabColors[initialTab]?.slider || tabColors.index.slider;
      currentTabIndex.value = tabIndex; // 同步设置当前tab索引

      // 初始化图标缩放状态 - 确保当前选中的图标有正确的缩放值
      iconScales.forEach((scale, index) => {
        if (index === tabIndex) {
          scale.value = 1.08; // 选中状态的缩放值
        } else {
          scale.value = 1; // 未选中状态的缩放值
        }
      });

      console.log('初始滑块位置已设置:', { initialTab, tabIndex, targetPosition });
    }
  }, [user, ready, sliderTranslateX, sliderColor, screenWidth, iconScales]);



  useEffect(() => {
    const checkAndRedirect = async () => {
      if (authChecked && user) {
        console.log('Dashboard layout check:', {
          user: user.isGuest ? 'guest' : 'user',
          tab,
          activeTab,
          currentURL: typeof window !== 'undefined' ? window.location.href : 'N/A'
        });

        // 检查是否为游客用户
        if (user.isGuest) {
          // 游客用户允许访问的页面
          const allowedTabs = ['index', 'community', 'profile'];

          // 检查是否是第一次登录（游客第一次进入的情况）
          const isFirstLogin = await AsyncStorage.getItem('isFirstLogin');
          if (isFirstLogin === 'true' && !tab) {
            await AsyncStorage.removeItem('isFirstLogin');

            // 检查是否有上次访问的页面（游客允许的页面）
            const lastRoute = await AsyncStorage.getItem('lastVisitedRoute');
            if (lastRoute && lastRoute.includes('tab=')) {
              const tabMatch = lastRoute.match(/tab=(\w+)/);
              const lastTab = tabMatch ? tabMatch[1] : 'index';

              // 确保游客只能访问允许的页面
              if (allowedTabs.includes(lastTab)) {
                console.log('Dashboard: 游客恢复到上次访问的页面:', lastTab);
                setActiveTab(lastTab);
                setSliderPosition(lastTab, true);

                // 初始化图标缩放状态
                const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
                const tabIndex = tabIndexMap[lastTab] || 0;
                iconScales.forEach((scale, index) => {
                  scale.value = index === tabIndex ? 1.08 : 1;
                });

                setReady(true);
                router.setParams({ tab: lastTab });
                return;
              }
            }

            // 没有有效的上次访问记录，跳转到index页面
            console.log('Dashboard: 游客第一次登录，跳转到index页面');
            setActiveTab('index');
            setSliderPosition('index', true);

            // 初始化图标缩放状态
            iconScales.forEach((scale, index) => {
              scale.value = index === 0 ? 1.08 : 1; // index对应索引0
            });

            setReady(true);
            router.setParams({ tab: 'index' });
            return;
          }

          // 如果tab不被允许，重定向到index
          if (tab && !allowedTabs.includes(tab)) {
            console.log('游客用户访问受限tab:', tab, '重定向到index');
            setActiveTab('index');
            setSliderPosition('index', true);

            // 初始化图标缩放状态
            iconScales.forEach((scale, index) => {
              scale.value = index === 0 ? 1.08 : 1; // index对应索引0
            });

            setReady(true);
            router.setParams({ tab: 'index' });
            return;
          }

          // 如果有有效的tab参数，使用它
          if (tab && allowedTabs.includes(tab)) {
            console.log('Dashboard: 游客有有效的tab参数:', tab);
            setActiveTab(tab);
            setSliderPosition(tab, true);

            // 初始化图标缩放状态
            const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
            const tabIndex = tabIndexMap[tab] || 0;
            iconScales.forEach((scale, index) => {
              scale.value = index === tabIndex ? 1.08 : 1;
            });

            // 保存当前路由
            const currentRoute = `/(dashboard)/?tab=${tab}`;
            AsyncStorage.setItem('lastVisitedRoute', currentRoute);
          } else if (!tab) {
            // 没有tab参数，尝试恢复上次访问的页面
            console.log('Dashboard: 游客没有tab参数，尝试恢复上次访问的页面');
            try {
              const lastRoute = await AsyncStorage.getItem('lastVisitedRoute');
              if (lastRoute && lastRoute.includes('tab=')) {
                const tabMatch = lastRoute.match(/tab=(\w+)/);
                const lastTab = tabMatch ? tabMatch[1] : 'index';

                // 确保游客只能访问允许的页面
                if (allowedTabs.includes(lastTab)) {
                  console.log('Dashboard: 游客恢复到上次访问的页面:', lastTab);
                  setActiveTab(lastTab);
                  setSliderPosition(lastTab, true);

                  // 初始化图标缩放状态
                  const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
                  const tabIndex = tabIndexMap[lastTab] || 0;
                  iconScales.forEach((scale, index) => {
                    scale.value = index === tabIndex ? 1.08 : 1;
                  });

                  router.setParams({ tab: lastTab });
                } else {
                  console.log('Dashboard: 游客上次访问的页面不被允许，使用默认index');
                  setActiveTab('index');
                  setSliderPosition('index', true);

                  // 初始化图标缩放状态
                  iconScales.forEach((scale, index) => {
                    scale.value = index === 0 ? 1.08 : 1; // index对应索引0
                  });

                  router.setParams({ tab: 'index' });
                }
              } else {
                console.log('Dashboard: 游客没有上次访问记录，使用默认index');
                setActiveTab('index');
                setSliderPosition('index', true);

                // 初始化图标缩放状态
                iconScales.forEach((scale, index) => {
                  scale.value = index === 0 ? 1.08 : 1; // index对应索引0
                });

                router.setParams({ tab: 'index' });
              }
            } catch (error) {
              console.error('Dashboard: 游客恢复页面失败:', error);
              setActiveTab('index');
              setSliderPosition('index', true);
              router.setParams({ tab: 'index' });
            }
          }

          setReady(true);
          return;
        }

        // 正常用户的逻辑
        const userProfileKey = `profileSetupCompleted_${user.userAccount}`;
        const setupCompleted = await AsyncStorage.getItem(userProfileKey);
        const isCompleted = setupCompleted === "true";
        if (!isCompleted) {
          setReady(false);
          router.replace('/profile-setup');
          return;
        }

        // 检查是否是第一次登录（用户再次登录的情况）
        const isFirstLogin = await AsyncStorage.getItem('isFirstLogin');
        if (isFirstLogin === 'true' && !tab) {
          await AsyncStorage.removeItem('isFirstLogin');

          // 检查是否有上次访问的页面
          const lastRoute = await AsyncStorage.getItem('lastVisitedRoute');
          if (lastRoute && lastRoute.includes('tab=')) {
            // 恢复到上次访问的页面
            const tabMatch = lastRoute.match(/tab=(\w+)/);
            const lastTab = tabMatch ? tabMatch[1] : 'plans';
            console.log('Dashboard: 恢复到上次访问的页面:', lastTab);
            setActiveTab(lastTab);
            setSliderPosition(lastTab, true);
            setReady(true);
            router.setParams({ tab: lastTab });
            return;
          } else {
            // 没有上次访问记录，跳转到plans页面
            console.log('Dashboard: 第一次登录，跳转到plans页面');
            setActiveTab('plans');
            setSliderPosition('plans', true);
            setReady(true);
            router.setParams({ tab: 'plans' });
            return;
          }
        }

        // 如果有有效的tab参数，使用该tab
        if (tab && (tab === "plans" || tab === "index" || tab === "community" || tab === "profile")) {
          console.log('Dashboard: 有有效的tab参数:', tab);
          setActiveTab(tab);
          setSliderPosition(tab, true);

          // 保存当前路由
          const currentRoute = tab === 'index' ? '/(dashboard)/?tab=index' : `/(dashboard)/${tab}?tab=${tab}`;
          AsyncStorage.setItem('lastVisitedRoute', currentRoute);
        } else if (!tab) {
          // 没有tab参数，尝试恢复上次访问的页面
          console.log('Dashboard: 没有tab参数，尝试恢复上次访问的页面');
          try {
            const lastRoute = await AsyncStorage.getItem('lastVisitedRoute');
            if (lastRoute && lastRoute.includes('tab=')) {
              const tabMatch = lastRoute.match(/tab=(\w+)/);
              const lastTab = tabMatch ? tabMatch[1] : 'index';
              console.log('Dashboard: 恢复到上次访问的页面:', lastTab);
              setActiveTab(lastTab);
              setSliderPosition(lastTab, true);
              router.setParams({ tab: lastTab });
            } else {
              console.log('Dashboard: 没有上次访问记录，使用默认index');
              setActiveTab('index');
              setSliderPosition('index', true);
              router.setParams({ tab: 'index' });
            }
          } catch (error) {
            console.error('Dashboard: 恢复页面失败:', error);
            setActiveTab('index');
            setSliderPosition('index', true);
            router.setParams({ tab: 'index' });
          }
        }

        setReady(true);
      }
    };
    checkAndRedirect();
  }, [authChecked, user, tab, router]);

  // handleTabPress逻辑 - 带权限检查版本
  const handleTabPress = useCallback((tabName) => {
    console.log('Tab点击:', tabName, '用户类型:', user?.isGuest ? '游客' : '正式用户');

    // 游客权限检查
    if (user?.isGuest && tabName === 'plans') {
      console.log('游客尝试访问plans页面，显示权限提示');
      tryAccessPage('plans');
      return;
    }

    if (tabName === 'more') {
      if (!showMorePanel) {
        setTimeout(() => setShowMorePanel(true), 300);
      } else {
        setShowMorePanel(false);
      }

      // 确保滑块移动到 more 位置 - 使用统一的动效
      const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
      const tabIndex = tabIndexMap['more']; // 固定为2
      const targetPosition = actualTabWidth * tabIndex;

      // 同步more图标的所有动画
      sliderTranslateX.value = withTiming(targetPosition, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      });
      sliderColor.value = withTiming(tabColors.more.slider, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      });
      // 更新当前tab索引，触发图标动画
      currentTabIndex.value = withTiming(tabIndex, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      });

      // more 图标的缩放动画 - 与其他tab保持一致
      iconScales.forEach((scale, index) => {
        if (index === tabIndex) {
          scale.value = withTiming(1.08, {
            duration: 200,
            easing: Easing.out(Easing.quad)
          });
        } else {
          scale.value = withTiming(1, {
            duration: 200,
            easing: Easing.out(Easing.quad)
          });
        }
      });

      return;
    }

    if (showMorePanel) setShowMorePanel(false);

    console.log('Tab点击处理:', tabName, '当前activeTab:', activeTab);

    // 计算tabIndex用于图标动画
    const tabIndexMap = { 'index': 0, 'plans': 1, 'more': 2, 'community': 3, 'profile': 4 };
    const tabIndex = tabIndexMap[tabName] || 0;

    // 同步所有动画 - 同时更新滑块位置、颜色和图标状态
    const targetPosition = actualTabWidth * tabIndex;

    console.log('设置同步动画:', {
      tabName,
      tabIndex,
      targetPosition,
      currentPosition: sliderTranslateX.value,
      actualTabWidth
    });

    // 同时触发所有动画，确保完美同步
    sliderTranslateX.value = withTiming(targetPosition, {
      duration: 200,
      easing: Easing.out(Easing.quad)
    });
    sliderColor.value = withTiming(tabColors[tabName]?.slider || tabColors.index.slider, {
      duration: 200,
      easing: Easing.out(Easing.quad)
    });
    // 更新当前tab索引，触发图标动画
    currentTabIndex.value = withTiming(tabIndex, {
      duration: 200,
      easing: Easing.out(Easing.quad)
    });

    // 优化图标缩放动画 - 与滑块动画保持一致的性能参数
    iconScales.forEach((scale, index) => {
      if (index === tabIndex) {
        // 轻微的缩放效果，使用优化的动画参数
        scale.value = withTiming(1.08, {
          duration: 200,
          easing: Easing.out(Easing.quad)
        });
      } else {
        scale.value = withTiming(1, {
          duration: 200,
          easing: Easing.out(Easing.quad)
        });
      }
    });

    // 延迟设置activeTab，让滑块动画先执行
    setTimeout(() => {
      setActiveTab(tabName);
      router.setParams({ tab: tabName });
    }, 50); // 短暂延迟，让动画开始

    // 保存当前路由，用于刷新或重新登录时恢复
    const currentRoute = tabName === 'index' ? '/(dashboard)/?tab=index' : `/(dashboard)/${tabName}?tab=${tabName}`;
    AsyncStorage.setItem('lastVisitedRoute', currentRoute);
  }, [router, iconScales, sliderTranslateX, sliderColor, screenWidth, showMorePanel,
    setShowMorePanel, user, tryAccessPage, tabColors]);

  const sliderAnimatedStyle = useAnimatedStyle(() => {
    const sliderSize = 60;
    // 让滑块居中于当前tab - 使用全局的actualTabWidth确保一致性
    const centerX = sliderTranslateX.value + actualTabWidth / 2 - sliderSize / 2;
    return {
      transform: [
        { translateX: centerX },
        { scale: sliderScale.value }
      ],
      backgroundColor: sliderColor.value,
      width: sliderSize,
      height: sliderSize,
      borderRadius: sliderSize / 2,
      position: 'absolute',
      top: 15,
      zIndex: 0,
      alignSelf: 'flex-start',
    };
  });

  const tabItems = [
    {
      name: 'index',
      title: '主页',
      icon: require('../../assets/Tabbar/Index_Study_0.png'),
      iconActive: require('../../assets/Tabbar/Index_study_1.png')
    },
    {
      name: 'plans',
      title: '计划',
      icon: require('../../assets/Tabbar/Plan_crown.png'),
      iconActive: require('../../assets/Tabbar/Plan_crown_1.png')
    },
    {
      name: 'more',
      title: '更多',
      icon: require('../../assets/Tabbar/More_0.png'),
      iconActive: require('../../assets/Tabbar/More_1.png')
    },
    {
      name: 'community',
      title: '社区',
      icon: require('../../assets/Tabbar/Community_dog_scratch_0.png'),
      iconActive: require('../../assets/Tabbar/Community_dog_scratch_1.png')
    },
    {
      name: 'profile',
      title: '我的',
      icon: require('../../assets/Tabbar/Mine_smiling_0.png'),
      iconActive: require('../../assets/Tabbar/Mine_smiling_1.png')
    }
  ];

  if (!ready) {
    return null;
  }

  // 渲染所有页面，通过样式控制显示/隐藏，避免组件重新挂载
  const renderAllPages = () => {
    return (
      <>
        <View style={[styles.pageContainer, { display: activeTab === 'index' ? 'flex' : 'none' }]}>
          <IndexPage />
        </View>
        <View style={[styles.pageContainer, { display: activeTab === 'plans' ? 'flex' : 'none' }]}>
          <PlansPage />
        </View>
        <View style={[styles.pageContainer, { display: activeTab === 'community' ? 'flex' : 'none' }]}>
          <CommunityPage />
        </View>
        <View style={[styles.pageContainer, { display: activeTab === 'profile' ? 'flex' : 'none' }]}>
          <ProfilePage />
        </View>
      </>
    );
  };

  return (
    <UserOnly>
      <View style={styles.container}>
        {/* 页面内容 */}
        <View style={styles.content}>
          {renderAllPages()}
        </View>

        {/* 标签栏 */}
        <View style={[styles.tabBar, { height: showMorePanel ? 150 : 90 }]}>
          {/* More面板 - 三个横向盒子 */}
          {showMorePanel && (
            <Animated.View style={[styles.morePanelRow, morePanelAnimatedStyle]}>
              <TouchableOpacity style={styles.morePanelItem}>
                <ThemedText style={styles.morePanelText}>学习</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity style={styles.morePanelItem}>
                <ThemedText style={styles.morePanelText}>目标</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity style={styles.morePanelItem}>
                <ThemedText style={styles.morePanelText}>设置</ThemedText>
              </TouchableOpacity>
            </Animated.View>
          )}

          <View style={styles.tabRow}>
            <Animated.View style={[styles.slider, sliderAnimatedStyle]} />

            {tabItems.map((item, index) => (
              <TouchableOpacity
                key={item.name}
                style={styles.tabButton}
                onPress={() => handleTabPress(item.name)}
                activeOpacity={0.7}
              >
                <View style={styles.tabInnerWrap}>
                  <Animated.View style={iconAnimatedStyles[index]}>
                    {/* 图标容器 - 确保正确的层叠和清理 */}
                    <View style={styles.iconContainer}>
                      {/* 未选中状态的图标 */}
                      <Animated.Image
                        source={item.icon}
                        style={[
                          styles.tabIcon,
                          iconInactiveStyles[index]
                        ]}
                        resizeMode="contain"
                      />
                      {/* 选中状态的图标 */}
                      <Animated.Image
                        source={item.iconActive}
                        style={[
                          styles.tabIcon,
                          styles.tabIconActive,
                          iconActiveStyles[index]
                        ]}
                        resizeMode="contain"
                      />
                    </View>
                  </Animated.View>
                  <ThemedText style={styles.tabLabel}>{item.title}</ThemedText>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* 游客权限提示弹窗 */}
      <GuestPermissionModal
        visible={showPermissionModal}
        onClose={closePermissionModal}
        onRegister={handleRegister}
        featureName={currentFeature.name}
        description={currentFeature.description}
      />
    </UserOnly>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#transparent',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.background, // 使用Colors.js中的背景色
    // 移除固定的 marginBottom，让内容能够延伸到底部
  },
  pageContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  tabBar: {
    flexDirection: 'column',
    paddingBottom: 20, // tabbar下方的20padding
    position: 'absolute',
    bottom: 20, // 底部20间距，让页面内容可见
    left: 20,
    right: 20,
    borderRadius: 55,
    backgroundColor: Colors.button, // 使用Colors.js中的按钮颜色
    ...shadowPresets.heavy,
    zIndex: 1000, // 确保tabbar层级最高
  },
  morePanelRow: {
    flexDirection: 'row',
    height: 50,
    paddingHorizontal: 20,
    paddingVertical: 8,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  tabRow: {
    flexDirection: 'row',
    height: 90,
  },
  slider: {
    position: 'absolute',
    top: 15,
    left: 0,
    width: 60,
    height: 60,
    borderRadius: 30,
    zIndex: 0,
    alignSelf: 'center',
  },
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: 90,
    zIndex: 1,
  },
  tabInnerWrap: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    width: '100%',
    zIndex: 1,
  },
  iconContainer: {
    position: 'relative',
    width: 28,
    height: 28,
    marginBottom: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabIcon: {
    width: 28,
    height: 28,
    zIndex: 1,
  },
  tabIconActive: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 28,
    height: 28,
    zIndex: 2,
  },
  tabLabel: {
    fontSize: 10,
    color: '#8B4513',
    marginTop: 2,
    fontWeight: '500',
    textAlign: 'center',
  },
  morePanelItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    marginHorizontal: 8,
    backgroundColor: Colors.lightOrange, // 使用Colors.js中的浅橙色
    borderRadius: 16,
    minHeight: 34,
  },
  morePanelText: {
    fontSize: 14,
    color: '#8B4513',
    fontWeight: '500',
    textAlign: 'center',
  },
});


